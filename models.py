from sqlalchemy import create_engine, Column, Integer, String, DateTime, Float, Boolean, ForeignKey, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import enum
from datetime import datetime

Base = declarative_base()

class DrivingLicenseType(enum.Enum):
    C1 = "C1"
    C2 = "C2"

class County(enum.Enum):
    CHALING = "茶陵"
    YANLING = "炎陵"
    YOUXIAN = "攸县"
    OTHER = "其它"

class ExamSubject(enum.Enum):
    SUBJECT_TWO = "科目二"
    SUBJECT_THREE = "科目三"

class ExamLine(enum.Enum):
    LINE_ONE = "线路一"
    LINE_TWO = "线路二"
    LINE_THREE = "线路三"

# 添加预约状态枚举
class AppointmentStatus(enum.Enum):
    PENDING = "待支付"
    PAID = "已支付"
    CANCELLED = "已取消"
    COMPLETED = "已完成"

class DrivingSchool(Base):
    __tablename__ = 'driving_schools'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    address = Column(String(200))
    contact = Column(String(20))
    
    # 关联学员
    students = relationship("Student", back_populates="school")

class Student(Base):
    __tablename__ = 'students'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    id_card = Column(String(18), unique=True, nullable=False)
    phone = Column(String(11), nullable=False)
    license_type = Column(Enum(DrivingLicenseType), nullable=False)
    county = Column(Enum(County), default=County.CHALING)
    
    # 外键关联驾校
    school_id = Column(Integer, ForeignKey('driving_schools.id'))
    school = relationship("DrivingSchool", back_populates="students")
    
    # 关联预约记录
    appointments = relationship("Appointment", back_populates="student")

class ExamSession(Base):
    __tablename__ = 'exam_sessions'
    
    id = Column(Integer, primary_key=True)
    subject = Column(Enum(ExamSubject), nullable=False)
    exam_date = Column(DateTime, nullable=False)
    exam_line = Column(Enum(ExamLine))  # 科目三才有线路
    max_capacity = Column(Integer, default=10)
    price = Column(Float, nullable=False)  # 每次预约的价格
    is_active = Column(Boolean, default=True)  # 是否开放预约
    
    # 关联预约
    appointments = relationship("Appointment", back_populates="session")

class Appointment(Base):
    __tablename__ = 'appointments'
    
    id = Column(Integer, primary_key=True)
    appointment_time = Column(DateTime, default=datetime.now)
    status = Column(Enum(AppointmentStatus), default=AppointmentStatus.PENDING)
    payment_time = Column(DateTime)
    payment_method = Column(String(20))  # 支付方式（支付宝等）
    transaction_id = Column(String(100))  # 交易号
    
    # 外键关联
    student_id = Column(Integer, ForeignKey('students.id'))
    session_id = Column(Integer, ForeignKey('exam_sessions.id'))
    
    student = relationship("Student", back_populates="appointments")
    session = relationship("ExamSession", back_populates="appointments")

# 添加管理员模型
class Admin(Base):
    __tablename__ = 'admins'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    name = Column(String(50), nullable=False)
    role = Column(String(20), default="admin")  # admin, super_admin
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)

# 添加统计模型
class AppointmentStatistics(Base):
    __tablename__ = 'appointment_statistics'
    
    id = Column(Integer, primary_key=True)
    stat_date = Column(DateTime, nullable=False)
    subject = Column(Enum(ExamSubject), nullable=False)
    total_appointments = Column(Integer, default=0)
    paid_appointments = Column(Integer, default=0)
    cancelled_appointments = Column(Integer, default=0)
    completed_appointments = Column(Integer, default=0)
    total_revenue = Column(Float, default=0.0)
    county_stats = Column(String(500))  # 各区县预约情况JSON字符串
    license_type_stats = Column(String(200))  # 驾照类型统计JSON字符串

# 创建数据库
engine = create_engine('sqlite:///driving_exam.db')
Base.metadata.create_all(engine)
Session = sessionmaker(bind=engine)
